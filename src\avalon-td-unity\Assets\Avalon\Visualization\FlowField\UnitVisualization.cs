using Unity.Entities;
using Unity.Mathematics;
using Unity.Collections;
using UnityEngine;
using Unity.Transforms;
using Unity.Deterministic.Mathematics;
using System.Collections.Generic;
using FlowField;
using Avalon.Simulation.Movement;
using Avalon.Simulation.SiegeSlots;

namespace Avalon.Visualization.FlowField
{
    /// <summary>
    /// Enhanced unit visualization component for the Avalon system.
    /// Handles unit rendering, velocity vectors, paths, and siege slot connections.
    /// </summary>
    public class UnitVisualization : BaseFlowFieldVisualization
    {
        [Header("Unit Display Options")]
        [SerializeField] private bool showUnits = true;
        [SerializeField] private bool showUnitVelocity = true;
        [SerializeField] private bool showUnitDesiredDirection = true;
        [SerializeField] private bool showAvoidanceForces = true;
        [SerializeField] private bool showProximityThreshold = true;
        [SerializeField] private bool showAvoidanceRadius = true;
        
        [Header("Path and Target Visualization")]
        [SerializeField] private bool showUnitPaths = false;
        [SerializeField] private bool showTargetLines = true;
        [SerializeField] private bool showSiegeSlotConnections = true;
        [SerializeField] private bool showMovementTargets = true;
        [SerializeField] private bool showPathPrediction = false;
        
        [Header("Unit Colors")]
        [SerializeField] private Color unitColor = Color.blue;
        [SerializeField] private Color nearTargetColor = Color.green;
        [SerializeField] private Color velocityColor = Color.cyan;
        [SerializeField] private Color desiredDirectionColor = Color.magenta;
        [SerializeField] private Color avoidanceColor = Color.orange;
        [SerializeField] private Color proximityColor = Color.green;
        
        [Header("Path and Target Colors")]
        [SerializeField] private Color pathColor = Color.yellow;
        [SerializeField] private Color targetLineColor = Color.red;
        [SerializeField] private Color siegeSlotLineColor = Color.purple;
        [SerializeField] private Color movementTargetColor = Color.cyan;
        [SerializeField] private Color predictedPathColor = Color.white;
        
        [Header("Unit Properties")]
        [SerializeField] private float unitSize = 0.5f;
        [SerializeField] private float velocityScale = 1.0f;
        [SerializeField] private float proximityAlpha = 0.2f;
        [SerializeField] private float avoidanceAlpha = 0.1f;
        [SerializeField] private float pathLineWidth = 2.0f;
        [SerializeField] private float targetLineWidth = 1.5f;
        
        [Header("Path Settings")]
        [SerializeField] private int maxPathPoints = 50;
        [SerializeField] private float pathPointSpacing = 0.5f;
        [SerializeField] private float pathPredictionDistance = 10f;
        [SerializeField] private bool showPathHistory = true;
        [SerializeField] private float pathHistoryDuration = 5f;
        
        [Header("Unit Configuration")]
        [SerializeField] private List<Entity> unitsToVisualize = new List<Entity>();
        [SerializeField] private Entity targetEntity = Entity.Null;
        [SerializeField] private bool autoFindUnits = true;
        [SerializeField] private bool showNonAvoidanceUnits = true;
        [SerializeField] private bool showOnlySelectedUnit = false;
        [SerializeField] private Entity selectedUnit = Entity.Null;
        
        // Queries and caches
        private EntityQuery unitQuery;
        private EntityQuery siegeSlotQuery;
        private Dictionary<Entity, List<Vector3>> unitPathHistory = new Dictionary<Entity, List<Vector3>>();
        private Dictionary<Entity, float> lastPathUpdateTime = new Dictionary<Entity, float>();
        
        public override string VisualizationName => "Unit Visualization";
        
        #region Properties
        
        public List<Entity> UnitsToVisualize 
        { 
            get => unitsToVisualize; 
            set => unitsToVisualize = value; 
        }
        
        public Entity TargetEntity 
        { 
            get => targetEntity; 
            set => targetEntity = value; 
        }
        
        public bool ShowUnits 
        { 
            get => showUnits; 
            set => showUnits = value; 
        }
        
        public bool ShowUnitVelocity 
        { 
            get => showUnitVelocity; 
            set => showUnitVelocity = value; 
        }
        
        public bool ShowUnitDesiredDirection 
        { 
            get => showUnitDesiredDirection; 
            set => showUnitDesiredDirection = value; 
        }
        
        public bool ShowAvoidanceForces 
        { 
            get => showAvoidanceForces; 
            set => showAvoidanceForces = value; 
        }
        
        public bool ShowUnitPaths
        {
            get => showUnitPaths;
            set => showUnitPaths = value;
        }
        
        public bool ShowTargetLines
        {
            get => showTargetLines;
            set => showTargetLines = value;
        }
        
        public bool ShowSiegeSlotConnections
        {
            get => showSiegeSlotConnections;
            set => showSiegeSlotConnections = value;
        }
        
        public bool AutoFindUnits
        {
            get => autoFindUnits;
            set => autoFindUnits = value;
        }
        
        public Entity SelectedUnit
        {
            get => selectedUnit;
            set => selectedUnit = value;
        }
        
        public bool ShowOnlySelectedUnit
        {
            get => showOnlySelectedUnit;
            set => showOnlySelectedUnit = value;
        }
        
        #endregion
        
        #region Initialization and Updates
        
        protected override void OnInitialize()
        {
            CreateQueries();
        }
        
        protected override void OnRefreshData()
        {
            if (autoFindUnits && unitQuery.IsEmpty)
            {
                CreateQueries();
            }
            
            UpdatePathHistory();
        }
        
        protected override void OnMovementTypeChanged()
        {
            // Clear path history when movement type filter changes
            unitPathHistory.Clear();
            lastPathUpdateTime.Clear();
        }
        
        protected override void OnTargetChanged()
        {
            // Refresh target-related visualizations
            RefreshTargetConnections();
        }
        
        private void CreateQueries()
        {
            unitQuery = entityManager.CreateEntityQuery(
                typeof(LocalTransform),
                typeof(FlowFieldFollower)
            );
            
            siegeSlotQuery = entityManager.CreateEntityQuery(
                typeof(SiegeSlotData),
                typeof(SimulationTransform)
            );
        }
        
        private void UpdatePathHistory()
        {
            if (!showPathHistory || !showUnitPaths)
                return;
                
            var currentTime = Time.time;
            var unitsToProcess = GetUnitsToProcess();
            
            foreach (var unit in unitsToProcess)
            {
                if (!IsEntityValid(unit) || !entityManager.HasComponent<LocalTransform>(unit))
                    continue;
                    
                var transform = entityManager.GetComponentData<LocalTransform>(unit);
                UpdateUnitPathHistory(unit, transform.Position, currentTime);
            }
            
            // Clean up old path points
            CleanupOldPathPoints(currentTime);
        }
        
        private void UpdateUnitPathHistory(Entity unit, float3 position, float currentTime)
        {
            if (!unitPathHistory.ContainsKey(unit))
            {
                unitPathHistory[unit] = new List<Vector3>();
                lastPathUpdateTime[unit] = currentTime;
                return;
            }
            
            var pathHistory = unitPathHistory[unit];
            var lastUpdateTime = lastPathUpdateTime[unit];
            
            // Only add point if enough time has passed or distance is significant
            if (currentTime - lastUpdateTime >= pathPointSpacing || 
                (pathHistory.Count > 0 && Vector3.Distance(pathHistory[pathHistory.Count - 1], position) >= pathPointSpacing))
            {
                pathHistory.Add(position);
                lastPathUpdateTime[unit] = currentTime;
                
                // Limit path history length
                if (pathHistory.Count > maxPathPoints)
                {
                    pathHistory.RemoveAt(0);
                }
            }
        }
        
        private void CleanupOldPathPoints(float currentTime)
        {
            var unitsToRemove = new List<Entity>();
            
            foreach (var kvp in unitPathHistory)
            {
                var unit = kvp.Key;
                
                // Remove path history for units that no longer exist
                if (!IsEntityValid(unit))
                {
                    unitsToRemove.Add(unit);
                    continue;
                }
                
                // Remove old path points based on duration
                var pathHistory = kvp.Value;
                if (lastPathUpdateTime.TryGetValue(unit, out var lastUpdate))
                {
                    if (currentTime - lastUpdate > pathHistoryDuration)
                    {
                        pathHistory.Clear();
                    }
                }
            }
            
            foreach (var unit in unitsToRemove)
            {
                unitPathHistory.Remove(unit);
                lastPathUpdateTime.Remove(unit);
            }
        }
        
        private void RefreshTargetConnections()
        {
            // This could be expanded to cache target connection data
            // For now, connections are calculated on-demand during drawing
        }
        
        #endregion

        #region Drawing Methods

        protected override void OnDrawGizmos()
        {
            if (!showUnits)
                return;

            var unitsToProcess = GetUnitsToProcess();
            var elementsDrawn = 0;

            foreach (var unit in unitsToProcess)
            {
                if (elementsDrawn >= maxElementsToVisualize)
                    break;

                if (ShouldVisualizeEntity(unit))
                {
                    DrawUnit(unit);
                    elementsDrawn++;
                }
            }
        }

        public override string GetDetailedInfo()
        {
            if (!isInitialized)
                return "Unit Visualization not initialized";

            var unitCount = autoFindUnits ? GetUnitCountFromQuery() : unitsToVisualize.Count;
            var avoidanceUnitCount = autoFindUnits ? GetAvoidanceUnitCountFromQuery() : GetAvoidanceUnitCountFromList();
            var targetInfo = IsEntityValid(targetEntity) ? "Valid" : "Invalid/None";
            var selectedUnitInfo = IsEntityValid(selectedUnit) ? "Valid" : "None";
            var filterInfo = GetFilterDisplayName();

            var pathHistoryCount = unitPathHistory.Count;
            var totalPathPoints = 0;
            foreach (var pathHistory in unitPathHistory.Values)
            {
                totalPathPoints += pathHistory.Count;
            }

            return $"Units: {unitCount} (Avoidance: {avoidanceUnitCount})\n" +
                   $"Target: {targetInfo}\n" +
                   $"Selected Unit: {selectedUnitInfo}\n" +
                   $"Path History: {pathHistoryCount} units, {totalPathPoints} points\n" +
                   $"Filters: {filterInfo}\n" +
                   $"Show: Velocity={showUnitVelocity}, Paths={showUnitPaths}, Targets={showTargetLines}, Siege={showSiegeSlotConnections}";
        }

        private List<Entity> GetUnitsToProcess()
        {
            if (showOnlySelectedUnit && IsEntityValid(selectedUnit))
            {
                return new List<Entity> { selectedUnit };
            }

            if (autoFindUnits)
            {
                return GetUnitsFromQuery();
            }
            else
            {
                return unitsToVisualize;
            }
        }

        private List<Entity> GetUnitsFromQuery()
        {
            if (unitQuery.IsEmpty)
                CreateQueries();

            var entities = unitQuery.ToEntityArray(Allocator.Temp);
            var result = new List<Entity>();

            try
            {
                foreach (var unit in entities)
                {
                    result.Add(unit);
                }
            }
            finally
            {
                entities.Dispose();
            }

            return result;
        }

        private void DrawUnit(Entity unit)
        {
            if (!entityManager.HasComponent<LocalTransform>(unit) ||
                !entityManager.HasComponent<FlowFieldFollower>(unit))
                return;

            var transform = entityManager.GetComponentData<LocalTransform>(unit);
            var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
            var position = transform.Position;

            // Check if unit is near target
            bool isNearTarget = IsUnitNearTarget(position, follower);

            // Draw basic unit representation
            DrawBasicUnit(position, isNearTarget, follower.movementType);

            // Draw proximity threshold
            if (showProximityThreshold && follower.useAvoidance)
            {
                DrawProximityThreshold(position, follower);
            }

            // Draw avoidance radius
            if (showAvoidanceRadius && follower.useAvoidance)
            {
                DrawAvoidanceRadius(position, follower);
            }

            // Draw velocity vector
            if (showUnitVelocity)
            {
                DrawVelocityVector(unit, position);
            }

            // Draw desired direction
            if (showUnitDesiredDirection)
            {
                DrawDesiredDirection(unit, position);
            }

            // Draw avoidance forces
            if (showAvoidanceForces && follower.useAvoidance)
            {
                DrawAvoidanceForces(unit, position);
            }

            // Draw unit path
            if (showUnitPaths)
            {
                DrawUnitPath(unit, position);
            }

            // Draw target lines
            if (showTargetLines)
            {
                DrawTargetLine(unit, position);
            }

            // Draw siege slot connections
            if (showSiegeSlotConnections)
            {
                DrawSiegeSlotConnection(unit, position);
            }

            // Draw movement target
            if (showMovementTargets)
            {
                DrawMovementTarget(unit, position);
            }
        }

        private bool IsUnitNearTarget(float3 unitPosition, FlowFieldFollower follower)
        {
            if (!IsEntityValid(targetEntity))
                return false;

            if (!entityManager.HasComponent<LocalTransform>(targetEntity))
                return false;

            var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);
            var distanceToTarget = math.distance(unitPosition.xz, targetTransform.Position.xz);
            return distanceToTarget < (float)follower.targetProximityThreshold;
        }

        private void DrawBasicUnit(Vector3 position, bool isNearTarget, MovementType movementType)
        {
            // Use movement type color if filtering is enabled
            Color color = enableMovementTypeFilter ?
                FlowFieldVisualizationUtils.GetMovementTypeColor(movementType) :
                (isNearTarget ? nearTargetColor : unitColor);

            SetGizmosColor(color);
            Gizmos.DrawWireSphere(position, unitSize);

            // Draw a small indicator for the movement type
            if (enableMovementTypeFilter)
            {
                SetGizmosColor(color, 0.5f);
                Gizmos.DrawSphere(position + Vector3.up * (unitSize + 0.1f), 0.1f);
            }
        }

        private void DrawProximityThreshold(float3 position, FlowFieldFollower follower)
        {
            SetGizmosColor(proximityColor, proximityAlpha);
            FlowFieldVisualizationUtils.DrawCircle(position, (float)follower.targetProximityThreshold);
        }

        private void DrawAvoidanceRadius(float3 position, FlowFieldFollower follower)
        {
            SetGizmosColor(avoidanceColor, avoidanceAlpha);
            FlowFieldVisualizationUtils.DrawCircle(position, (float)follower.avoidanceRadius);
        }

        private void DrawVelocityVector(Entity unit, Vector3 position)
        {
            if (entityManager.HasComponent<AvoidanceData>(unit))
            {
                var avoidanceData = entityManager.GetComponentData<AvoidanceData>(unit);
                var velocityVector = FlowFieldVisualizationUtils.ToVector3(avoidanceData.velocity);

                if (velocityVector.magnitude > 0.01f)
                {
                    SetGizmosColor(velocityColor);
                    var endPos = position + velocityVector * velocityScale;
                    FlowFieldVisualizationUtils.DrawArrow(position, endPos, 0.2f);
                }
            }
        }

        private void DrawDesiredDirection(Entity unit, Vector3 position)
        {
            if (entityManager.HasComponent<FlowFieldFollower>(unit))
            {
                var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
                // This would need access to the flow field to get the desired direction
                // For now, we'll draw a placeholder
                SetGizmosColor(desiredDirectionColor);
                var endPos = position + Vector3.forward * 0.5f;
                Gizmos.DrawLine(position, endPos);
            }
        }

        private void DrawAvoidanceForces(Entity unit, Vector3 position)
        {
            if (entityManager.HasComponent<AvoidanceData>(unit))
            {
                var avoidanceData = entityManager.GetComponentData<AvoidanceData>(unit);
                var avoidanceForce = FlowFieldVisualizationUtils.ToVector3(avoidanceData.avoidanceForce);

                if (avoidanceForce.magnitude > 0.01f)
                {
                    SetGizmosColor(avoidanceColor);
                    var endPos = position + avoidanceForce * 0.5f;
                    FlowFieldVisualizationUtils.DrawArrow(position, endPos, 0.15f);
                }
            }
        }

        private void DrawUnitPath(Entity unit, Vector3 position)
        {
            // Draw path history
            if (showPathHistory && unitPathHistory.TryGetValue(unit, out var pathHistory))
            {
                if (pathHistory.Count > 1)
                {
                    SetGizmosColor(pathColor, 0.7f);

                    for (int i = 0; i < pathHistory.Count - 1; i++)
                    {
                        Gizmos.DrawLine(pathHistory[i], pathHistory[i + 1]);
                    }

                    // Connect last path point to current position
                    if (pathHistory.Count > 0)
                    {
                        Gizmos.DrawLine(pathHistory[pathHistory.Count - 1], position);
                    }
                }
            }

            // Draw predicted path
            if (showPathPrediction)
            {
                DrawPredictedPath(unit, position);
            }
        }

        private void DrawPredictedPath(Entity unit, Vector3 position)
        {
            // This would require access to flow field data to predict the path
            // For now, draw a simple forward prediction
            SetGizmosColor(predictedPathColor, 0.5f);

            var currentPos = position;
            var direction = Vector3.forward; // This should be calculated from flow field
            var stepSize = 0.5f;
            var steps = Mathf.RoundToInt(pathPredictionDistance / stepSize);

            for (int i = 0; i < steps; i++)
            {
                var nextPos = currentPos + direction * stepSize;
                Gizmos.DrawLine(currentPos, nextPos);
                currentPos = nextPos;
            }
        }

        private void DrawTargetLine(Entity unit, float3 position)
        {
            if (entityManager.HasComponent<MovementTarget>(unit))
            {
                var movementTarget = entityManager.GetComponentData<MovementTarget>(unit);

                if (movementTarget.targetId.HasValue)
                {
                    var targetEntity = FindTargetEntity(movementTarget.targetId.Value);
                    if (IsEntityValid(targetEntity) && entityManager.HasComponent<LocalTransform>(targetEntity))
                    {
                        var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);

                        SetGizmosColor(targetLineColor, 0.6f);
                        Gizmos.DrawLine(position, targetTransform.Position);

                        // Draw target marker
                        SetGizmosColor(movementTargetColor);
                        Gizmos.DrawWireSphere(targetTransform.Position, 0.3f);
                    }
                }
            }
        }

        private void DrawSiegeSlotConnection(Entity unit, Vector3 position)
        {
            if (entityManager.HasComponent<SiegeSlotAssignment>(unit))
            {
                var assignment = entityManager.GetComponentData<SiegeSlotAssignment>(unit);

                if (assignment.IsValid())
                {
                    var slotPosition = FlowFieldVisualizationUtils.ToVector3(assignment.SlotPosition);
                    var targetPosition = FlowFieldVisualizationUtils.ToVector3(assignment.TargetPosition);

                    // Choose color based on siege slot status
                    Color lineColor = assignment.status switch
                    {
                        SiegeSlotStatus.SlotReserved => Color.orange,
                        SiegeSlotStatus.MovingToSlot => Color.blue,
                        SiegeSlotStatus.OccupyingSlot => Color.red,
                        _ => Color.gray
                    };

                    SetGizmosColor(lineColor, 0.8f);

                    // Draw line from unit to assigned slot
                    if (assignment.SlotPosition != dfloat3.zero)
                    {
                        Gizmos.DrawLine(position, slotPosition);

                        // Draw slot marker
                        Gizmos.DrawWireSphere(slotPosition, 0.2f);
                    }

                    // Draw line from slot to target center
                    if (assignment.TargetPosition != dfloat3.zero && assignment.SlotPosition != dfloat3.zero)
                    {
                        SetGizmosColor(lineColor, 0.4f);
                        Gizmos.DrawLine(slotPosition, targetPosition);
                    }
                }
            }
        }

        private void DrawMovementTarget(Entity unit, Vector3 position)
        {
            if (entityManager.HasComponent<MovementTarget>(unit))
            {
                var movementTarget = entityManager.GetComponentData<MovementTarget>(unit);

                if (movementTarget.targetId.HasValue)
                {
                    // Find the target entity and get its position
                    var targetEntity = FindTargetEntity(movementTarget.targetId.Value);
                    if (IsEntityValid(targetEntity) && entityManager.HasComponent<LocalTransform>(targetEntity))
                    {
                        var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);
                        var targetPos = targetTransform.Position;

                        SetGizmosColor(movementTargetColor, 0.5f);
                        Gizmos.DrawWireCube(targetPos, Vector3.one * 0.3f);
                    }
                }
            }
        }

        #endregion

        #region Helper Methods

        private Entity FindTargetEntity(int targetId)
        {
            // Create a query to find target entities with the specified ID
            var targetQuery = entityManager.CreateEntityQuery(typeof(FlowFieldTarget), typeof(LocalTransform));
            var entities = targetQuery.ToEntityArray(Allocator.Temp);

            try
            {
                foreach (var entity in entities)
                {
                    if (entityManager.HasComponent<FlowFieldTarget>(entity))
                    {
                        var target = entityManager.GetComponentData<FlowFieldTarget>(entity);
                        if (target.targetId == targetId)
                        {
                            return entity;
                        }
                    }
                }
            }
            finally
            {
                entities.Dispose();
                targetQuery.Dispose();
            }

            return Entity.Null;
        }

        private int GetUnitCountFromQuery()
        {
            return unitQuery.IsEmpty ? 0 : unitQuery.CalculateEntityCount();
        }

        private int GetAvoidanceUnitCountFromQuery()
        {
            if (unitQuery.IsEmpty)
                return 0;

            var entities = unitQuery.ToEntityArray(Allocator.Temp);
            var count = 0;

            try
            {
                foreach (var unit in entities)
                {
                    if (entityManager.HasComponent<FlowFieldFollower>(unit))
                    {
                        var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
                        if (follower.useAvoidance)
                            count++;
                    }
                }
            }
            finally
            {
                entities.Dispose();
            }

            return count;
        }

        private int GetAvoidanceUnitCountFromList()
        {
            var count = 0;
            foreach (var unit in unitsToVisualize)
            {
                if (IsEntityValid(unit) && entityManager.HasComponent<FlowFieldFollower>(unit))
                {
                    var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
                    if (follower.useAvoidance)
                        count++;
                }
            }
            return count;
        }

        #endregion
    }
}
