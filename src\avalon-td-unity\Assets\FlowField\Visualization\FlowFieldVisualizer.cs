// using Unity.Entities;
// using Unity.Mathematics;
// using Unity.Collections;
// using UnityEngine;
// using System.Collections.Generic;
// using Unity.Deterministic.Mathematics;
// using Unity.Transforms;
// using FlowField.Visualization;
// using Avalon.Simulation.Movement;
//
// namespace FlowField
// {
//     /// <summary>
//     /// Coordinator component for flow field visualization.
//     /// Manages individual visualization components and provides easy enable/disable functionality.
//     /// </summary>
//     public class FlowFieldVisualizer : MonoBehaviour
//     {
//         [Header("Visualization Components")]
//         [SerializeField] private GridVisualization gridVisualization;
//         [SerializeField] private UnitVisualization unitVisualization;
//         [SerializeField] private TargetVisualization targetVisualization;
//         [SerializeField] private UnitPathVisualization unitPathVisualization;
//         [SerializeField] private ProjectileVisualization projectileVisualization;
//
//         [Header("Visualization Control")]
//         [SerializeField] private bool enableGridVisualization = true;
//         [SerializeField] private bool enableUnitVisualization = true;
//         [SerializeField] private bool enableTargetVisualization = true;
//         [SerializeField] private bool enableUnitPathVisualization = false;
//         [SerializeField] private bool enableProjectileVisualization = false;
//         [SerializeField] private bool autoCreateComponents = true;
//
//         [Header("Flow Field Setup")]
//         public int2 gridSize = new int2(20, 20);
//         public dfloat2 cellSize = new dfloat2(dfloat.One, dfloat.One);
//         public dfloat2 worldOrigin = new dfloat2(-(dfloat)10.0f, -(dfloat)10.0f);
//         public MovementType movementType = MovementType.Ground;
//         public int targetId = 0;
//
//         [Header("Test Setup")]
//         public bool createTestUnits = true;
//         public int testUnitCount = 10;
//         public float spawnRadius = 8.0f;
//
//         [Header("Avoidance Settings")]
//         public float targetProximityThreshold = 2.0f;
//
//         // Core ECS components
//         public World world;
//         public EntityManager entityManager;
//         public Entity flowFieldEntity;
//         public Entity targetEntity;
//         public List<Entity> testUnits = new List<Entity>();
//         public DynamicBuffer<FlowFieldCellBuffer> flowFieldBuffer;
//
//         // Visualization component references
//         private List<IVisualization> visualizationComponents = new List<IVisualization>();
// //
//         void Start()
//         {
//             SetupWorld();
//             CreateFlowField();
//             CreateTarget();
//
//             if (createTestUnits)
//             {
//                 CreateTestUnits();
//             }
//
//             InitializeVisualizationComponents();
//         }
//
//         void SetupWorld()
//         {
//             world = World.DefaultGameObjectInjectionWorld;
//             entityManager = world.EntityManager;
//         }
// //
// //         void CreateFlowField()
// //         {
// //             // Create flow field entity
// //             flowFieldEntity = entityManager.CreateEntity();
// //
// //             var flowFieldGrid = FlowFieldUtils.CreateFlowFieldGrid(
// //                 gridSize, cellSize, worldOrigin, targetEntity, targetId, movementType);
// //
// //             entityManager.AddComponentData(flowFieldEntity, flowFieldGrid);
// //             entityManager.AddBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //
// //             // Initialize buffer with empty cells
// //             flowFieldBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //             var cellCount = gridSize.x * gridSize.y;
// //             flowFieldBuffer.ResizeUninitialized(cellCount);
// //
// //             for (int i = 0; i < cellCount; i++)
// //             {
// //                 flowFieldBuffer[i] = new FlowFieldCellBuffer
// //                 {
// //                     cell = new FlowFieldCell
// //                     {
// //                         direction = new dfloat2(dfloat.Zero, dfloat.Zero),
// //                         cost = new dfloat(1.0f),
// //                         distance = new dfloat(float.MaxValue),
// //                         walkabilityMask = 0xFF, // All movement types
// //                         isTarget = false
// //                     }
// //                 };
// //             }
// //
// //             // Add some obstacles for testing
// //             CreateTestObstacles();
// //         }
// //
// //         void CreateTestObstacles()
// //         {
// //             // Create a few obstacle patterns
// //
// //             // Vertical wall
// //             for (int y = 5; y < 15; y++)
// //             {
// //                 SetObstacle(new int2(10, y), false);
// //             }
// //
// //             // L-shaped obstacle
// //             for (int x = 3; x < 7; x++)
// //             {
// //                 SetObstacle(new int2(x, 8), false);
// //             }
// //
// //             for (int y = 8; y < 12; y++)
// //             {
// //                 SetObstacle(new int2(6, y), false);
// //             }
// //
// //             // Scattered obstacles
// //             SetObstacle(new int2(15, 5), false);
// //             SetObstacle(new int2(16, 5), false);
// //             SetObstacle(new int2(15, 6), false);
// //         }
// //
// //         void SetObstacle(int2 gridPos, bool walkable)
// //         {
// //             if (gridPos.x < 0 || gridPos.x >= gridSize.x || gridPos.y < 0 || gridPos.y >= gridSize.y)
// //                 return;
// //
// //             var index = gridPos.y * gridSize.x + gridPos.x;
// //             var cell = flowFieldBuffer[index].cell;
// //             cell.SetWalkable(movementType, walkable);
// //             flowFieldBuffer[index] = new FlowFieldCellBuffer { cell = cell };
// //         }
// //
// //         void CreateTarget()
// //         {
// //             // Create target entity
// //             targetEntity = entityManager.CreateEntity();
// //             entityManager.AddComponentData(targetEntity, new LocalTransform
// //             {
// //                 Position = new float3(5.0f, 5.0f, 0.0f),
// //                 Rotation = quaternion.identity,
// //                 Scale = 1.0f
// //             });
// //
// //             entityManager.AddComponentData(targetEntity, new FlowFieldTarget
// //             {
// //                 targetId = targetId,
// //                 movementType = movementType,
// //                 isActive = true
// //             });
// //
// //             // Add SimulationTransform for deterministic target tracking
// //             entityManager.AddComponentData(targetEntity, new SimulationTransform
// //             {
// //                 position = new dfloat3(new dfloat(5.0f), new dfloat(5.0f), dfloat.Zero),
// //                 rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
// //                 scale = dfloat.One
// //             });
// //
// //             // Update flow field with target
// //             var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntity);
// //             flowFieldGrid.targetEntity = targetEntity;
// //             flowFieldGrid.needsUpdate = true;
// //             entityManager.SetComponentData(flowFieldEntity, flowFieldGrid);
// //         }
// //
// //         public void CreateTestUnits()
// //         {
// //             testUnits.Clear();
// //
// //             for (int i = 0; i < testUnitCount; i++)
// //             {
// //                 // Random position around the spawn area
// //                 var angle = (float)i / testUnitCount * 2 * math.PI;
// //                 var distance = UnityEngine.Random.Range(2.0f, spawnRadius);
// //                 var position = new float3(
// //                     math.cos(angle) * distance,
// //                     math.sin(angle) * distance,
// //                     0.0f
// //                 );
// //
// //                 Entity unit;
// //
// //                 if (useUnitTypeSystem && unitTypes.Count > 0)
// //                 {
// //                     // Use the new unit type system
// //                     unit = CreateUnitFromDefinition(position, i);
// //                 }
// //                 else
// //                 {
// //                     // Use the legacy system
// //                     unit = CreateLegacyUnit(position, i);
// //                 }
// //
// //                 testUnits.Add(unit);
// //             }
// //         }
// //
// //         private Entity CreateUnitFromDefinition(float3 position, int index)
// //         {
// //             // Select unit type
// //             UnitTypeDefinition unitTypeDef;
// //             if (randomizeUnitTypes)
// //             {
// //                 unitTypeDef = unitTypes[UnityEngine.Random.Range(0, unitTypes.Count)];
// //             }
// //             else
// //             {
// //                 unitTypeDef = unitTypes[index % unitTypes.Count];
// //             }
// //
// //             // Create unit entity
// //             var unit = entityManager.CreateEntity();
// //
// //             // Add LocalTransform
// //             entityManager.AddComponentData(unit, new LocalTransform
// //             {
// //                 Position = position,
// //                 Rotation = quaternion.identity,
// //                 Scale = 1.0f
// //             });
// //
// //             // Add UnitType component
// //             entityManager.AddComponentData(unit, new UnitType
// //             {
// //                 unitTypeId = unitTypeDef.unitTypeId,
// //                 prefabEntity = Entity.Null, // Will be set by visual system
// //                 name = unitTypeDef.unitName
// //             });
// //
// //             // Add UnitStats component
// //             entityManager.AddComponentData(unit, unitTypeDef.ToUnitStats());
// //
// //             // Add FlowFieldFollower component
// //             var movementType = unitTypeDef.GetMovementType();
// //             entityManager.AddComponentData(unit, unitTypeDef.ToFlowFieldFollower(targetId, movementType));
// //
// //             // Add AvoidanceData if needed
// //             var avoidanceData = unitTypeDef.ToAvoidanceData();
// //             if (avoidanceData.HasValue)
// //             {
// //                 entityManager.AddComponentData(unit, avoidanceData.Value);
// //             }
// //
// //             // Add remaining components
// //             AddStandardComponents(unit, position, unitTypeDef.ToFlowFieldFollower(targetId, movementType));
// //
// //             return unit;
// //         }
// //
// //         private Entity CreateLegacyUnit(float3 position, int index)
// //         {
// //             var unit = entityManager.CreateEntity();
// //
// //             entityManager.AddComponentData(unit, new LocalTransform
// //             {
// //                 Position = position,
// //                 Rotation = quaternion.identity,
// //                 Scale = 1.0f
// //             });
// //
// //             // Create different unit types with varied stats
// //             bool useAvoidance = index % 3 != 0; // 2/3 of units use avoidance
// //             var unitTypeId = index % 3; // Cycle through 3 different unit types
// //
// //             // Add UnitType component
// //             entityManager.AddComponentData(unit, new UnitType
// //             {
// //                 unitTypeId = unitTypeId,
// //                 prefabEntity = Entity.Null, // Will be set by visual system
// //                 name = $"TestUnit{unitTypeId}"
// //             });
// //
// //             // Add UnitStats component with varied stats based on unit type
// //             var baseSpeed = unitTypeId == 0 ? 3.0f : unitTypeId == 1 ? 5.0f : 7.0f;
// //             var baseHealth = unitTypeId == 0 ? 150.0f : unitTypeId == 1 ? 100.0f : 75.0f;
// //             var baseRadius = unitTypeId == 0 ? 0.7f : unitTypeId == 1 ? 0.5f : 0.3f;
// //
// //             entityManager.AddComponentData(unit, new UnitStats
// //             {
// //                 maxSpeed = new dfloat(baseSpeed + UnityEngine.Random.Range(-1.0f, 1.0f)),
// //                 acceleration = new dfloat(10.0f),
// //                 deceleration = new dfloat(8.0f),
// //                 rotationSpeed = new dfloat(5.0f),
// //                 maxHealth = new dfloat(baseHealth),
// //                 currentHealth = new dfloat(baseHealth),
// //                 attackDamage = new dfloat(10.0f),
// //                 attackRange = new dfloat(1.5f),
// //                 attackCooldown = new dfloat(1.0f),
// //                 radius = new dfloat(baseRadius),
// //                 mass = new dfloat(1.0f),
// //                 height = new dfloat(1.0f),
// //                 abilityFlags = 0
// //             });
// //
// //             var follower = new FlowFieldFollower
// //             {
// //                 maxSpeed = new dfloat(baseSpeed + UnityEngine.Random.Range(-1.0f, 1.0f)),
// //                 minSpeed = new dfloat(0.5f),
// //                 acceleration = new dfloat(10.0f),
// //                 deceleration = new dfloat(8.0f),
// //                 targetId = targetId,
// //                 movementType = movementType,
// //                 useAvoidance = useAvoidance,
// //                 flowFieldStrength = new dfloat(1.0f)
// //             };
// //
// //             if (useAvoidance)
// //             {
// //                 follower.avoidanceRadius = new dfloat(1.5f);
// //                 follower.separationStrength = new dfloat(2.0f);
// //                 follower.cohesionStrength = new dfloat(0.5f);
// //                 follower.alignmentStrength = new dfloat(0.3f);
// //                 follower.obstacleAvoidanceStrength = new dfloat(3.0f);
// //                 follower.avoidanceLayer = 0;
// //
// //                 // Set proximity threshold to disable avoidance when close to target
// //                 // This prevents units from circling around the target
// //                 follower.targetProximityThreshold = new dfloat(targetProximityThreshold);
// //
// //                 entityManager.AddComponentData(unit, new AvoidanceData
// //                 {
// //                     velocity = new dfloat2(dfloat.Zero, dfloat.Zero),
// //                     desiredDirection = new dfloat2(dfloat.Zero, dfloat.Zero),
// //                     avoidanceForce = new dfloat2(dfloat.Zero, dfloat.Zero),
// //                     radius = new dfloat(baseRadius),
// //                     lastAvoidanceTime = new dfloat(0)
// //                 });
// //             }
// //             else
// //             {
// //                 // Set proximity threshold for non-avoidance units too (for consistency)
// //                 follower.targetProximityThreshold = new dfloat(targetProximityThreshold * 0.5f);
// //             }
// //
// //             // Add remaining components
// //             AddStandardComponents(unit, position, follower);
// //
// //             return unit;
// //         }
// //
// //         private void AddStandardComponents(Entity unit, float3 position, FlowFieldFollower follower)
// //         {
// //             entityManager.AddComponentData(unit, follower);
// //
// //             // Add previous transform for interpolation - DETERMINISTIC
// //             var previousTransform = new PreviousTransform
// //             {
// //                 position = new dfloat3((dfloat)position.x, (dfloat)position.y, (dfloat)position.z),
// //                 rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One), // identity
// //                 scale = dfloat.One
// //             };
// //             entityManager.AddComponentData(unit, previousTransform);
// //
// //             // Add simulation transform (separate from rendering) - DETERMINISTIC
// //             var simulationTransform = new SimulationTransform
// //             {
// //                 position = new dfloat3((dfloat)position.x, (dfloat)position.y, (dfloat)position.z),
// //                 rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One), // identity
// //                 scale = dfloat.One
// //             };
// //             entityManager.AddComponentData(unit, simulationTransform);
// //         }
// //
// //         /// <summary>
// //         /// Force update the flow field - useful for testing and debugging
// //         /// </summary>
// //         public void ForceFlowFieldUpdate()
// //         {
// //             if (entityManager.Exists(flowFieldEntity))
// //             {
// //                 var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntity);
// //                 flowFieldGrid.needsUpdate = true;
// //                 entityManager.SetComponentData(flowFieldEntity, flowFieldGrid);
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Move the target to a new position and update the flow field
// //         /// </summary>
// //         public void MoveTarget(Vector3 newPosition)
// //         {
// //             if (entityManager.Exists(targetEntity))
// //             {
// //                 // Update LocalTransform for rendering
// //                 var transform = entityManager.GetComponentData<LocalTransform>(targetEntity);
// //                 transform.Position = newPosition;
// //                 entityManager.SetComponentData(targetEntity, transform);
// //
// //                 // Update SimulationTransform for deterministic simulation
// //                 var simTransform = entityManager.GetComponentData<SimulationTransform>(targetEntity);
// //                 simTransform.position =
// //                     new dfloat3((dfloat)newPosition.x, (dfloat)newPosition.y, (dfloat)newPosition.z);
// //                 entityManager.SetComponentData(targetEntity, simTransform);
// //
// //                 // Force flow field update
// //                 ForceFlowFieldUpdate();
// //             }
// //         }
// //
// //         void OnDrawGizmos()
// //         {
// //             if (!Application.isPlaying) return;
// //
// //             // Draw using the new modular visualization components
// //             foreach (var visualizationComponent in visualizationComponents)
// //             {
// //                 if (visualizationComponent != null && visualizationComponent.IsEnabled)
// //                 {
// //                     visualizationComponent.DrawGizmos();
// //                 }
// //             }
// //         }
// //
// //         void DrawGrid()
// //         {
// //             // Validate that we have valid data to draw
// //             if (!Application.isPlaying || !entityManager.Exists(flowFieldEntity)) return;
// //             if (!entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity)) return;
// //
// //             // Ensure we have the latest buffer reference
// //             flowFieldBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //             if (!flowFieldBuffer.IsCreated || flowFieldBuffer.Length == 0) return;
// //
// //             var originalColor = Gizmos.color;
// //
// //             // Draw grid lines
// //             Gizmos.color = new Color(gridColor.r, gridColor.g, gridColor.b, gridAlpha);
// //
// //             // Vertical lines
// //             for (int x = 0; x <= gridSize.x; x++)
// //             {
// //                 var worldX = (float)worldOrigin.x + x * (float)cellSize.x;
// //                 var startY = (float)worldOrigin.y;
// //                 var endY = (float)worldOrigin.y + gridSize.y * (float)cellSize.y;
// //
// //                 Gizmos.DrawLine(
// //                     new Vector3(worldX, startY, 0),
// //                     new Vector3(worldX, endY, 0)
// //                 );
// //             }
// //
// //             // Horizontal lines
// //             for (int y = 0; y <= gridSize.y; y++)
// //             {
// //                 var worldY = (float)worldOrigin.y + y * (float)cellSize.y;
// //                 var startX = (float)worldOrigin.x;
// //                 var endX = (float)worldOrigin.x + gridSize.x * (float)cellSize.x;
// //
// //                 Gizmos.DrawLine(
// //                     new Vector3(startX, worldY, 0),
// //                     new Vector3(endX, worldY, 0)
// //                 );
// //             }
// //
// //             // Draw cell contents
// //             for (int y = 0; y < gridSize.y; y++)
// //             {
// //                 for (int x = 0; x < gridSize.x; x++)
// //                 {
// //                     var index = y * gridSize.x + x;
// //                     var cell = flowFieldBuffer[index].cell;
// //
// //                     var cellCenter = new Vector3(
// //                         (float)worldOrigin.x + (x + 0.5f) * (float)cellSize.x,
// //                         (float)worldOrigin.y + (y + 0.5f) * (float)cellSize.y,
// //                         0
// //                     );
// //
// //                     // Draw obstacles
// //                     if (showWalkability && !cell.IsWalkableFor(movementType))
// //                     {
// //                         Gizmos.color = obstacleColor;
// //                         Gizmos.DrawCube(cellCenter,
// //                             new Vector3((float)cellSize.x * 0.9f, (float)cellSize.y * 0.9f, 0.1f));
// //                     }
// //
// //                     // Draw target
// //                     if (cell.isTarget)
// //                     {
// //                         Gizmos.color = targetColor;
// //                         Gizmos.DrawSphere(cellCenter, (float)cellSize.x * 0.3f);
// //                     }
// //
// //                     // Draw flow directions
// //                     if (showDirections && cell.direction.x != dfloat.Zero || cell.direction.y != dfloat.Zero)
// //                     {
// //                         Gizmos.color = directionColor;
// //                         var direction = new Vector3((float)cell.direction.x, (float)cell.direction.y, 0);
// //                         var arrowEnd = cellCenter + direction * (float)cellSize.x * arrowScale * 0.4f;
// //
// //                         Gizmos.DrawLine(cellCenter, arrowEnd);
// //
// //                         // Draw arrowhead
// //                         var arrowHead1 = arrowEnd - direction * 0.2f + Vector3.Cross(direction, Vector3.forward) * 0.1f;
// //                         var arrowHead2 = arrowEnd - direction * 0.2f - Vector3.Cross(direction, Vector3.forward) * 0.1f;
// //
// //                         Gizmos.DrawLine(arrowEnd, arrowHead1);
// //                         Gizmos.DrawLine(arrowEnd, arrowHead2);
// //                     }
// //
// //                     // Draw costs
// //                     if (showCosts)
// //                     {
// //                         var costAlpha = Mathf.Clamp01((float)cell.cost - 1.0f);
// //                         if (costAlpha > 0)
// //                         {
// //                             Gizmos.color = new Color(1, 0, 0, costAlpha * 0.5f);
// //                             Gizmos.DrawCube(cellCenter,
// //                                 new Vector3((float)cellSize.x * 0.8f, (float)cellSize.y * 0.8f, 0.05f));
// //                         }
// //                     }
// //                 }
// //             }
// //
// //             Gizmos.color = originalColor;
// //         }
// //
// //         void DrawUnits()
// //         {
// //             if (testUnits == null) return;
// //
// //             var originalColor = Gizmos.color;
// //
// //             foreach (var unit in testUnits)
// //             {
// //                 if (!entityManager.Exists(unit)) continue;
// //
// //                 var transform = entityManager.GetComponentData<LocalTransform>(unit);
// //                 var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
// //                 var position = transform.Position;
// //
// //                 // Check if unit is near target (for proximity visualization)
// //                 bool isNearTarget = false;
// //                 if (targetEntity != Entity.Null && entityManager.Exists(targetEntity))
// //                 {
// //                     var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);
// //                     var distanceToTarget = math.distance(position.xy, targetTransform.Position.xy);
// //                     isNearTarget = distanceToTarget < (float)follower.targetProximityThreshold;
// //                 }
// //
// //                 // Draw unit with different color if near target
// //                 Gizmos.color = isNearTarget ? Color.green : unitColor;
// //                 Gizmos.DrawWireSphere(position, unitSize);
// //
// //                 // Draw proximity threshold circle for avoidance units
// //                 if (follower.useAvoidance && showAvoidanceForces)
// //                 {
// //                     Gizmos.color = new Color(0, 1, 0, 0.2f); // Semi-transparent green
// //                     Gizmos.DrawWireSphere(position, (float)follower.targetProximityThreshold);
// //                 }
// //
// //                 // Draw unit info for avoidance units
// //                 if (follower.useAvoidance && entityManager.HasComponent<AvoidanceData>(unit))
// //                 {
// //                     var avoidance = entityManager.GetComponentData<AvoidanceData>(unit);
// //
// //                     // Draw velocity
// //                     if (showUnitVelocity)
// //                     {
// //                         Gizmos.color = velocityColor;
// //                         var velocityEnd = position +
// //                                           new float3((float)avoidance.velocity.x, (float)avoidance.velocity.y, 0) *
// //                                           velocityScale;
// //                         Gizmos.DrawLine(position, velocityEnd);
// //                         Gizmos.DrawWireSphere(velocityEnd, 0.1f);
// //                     }
// //
// //                     // Draw desired direction
// //                     if (showUnitDesiredDirection)
// //                     {
// //                         Gizmos.color = desiredDirectionColor;
// //                         var desiredEnd = position + new float3((float)avoidance.desiredDirection.x,
// //                             (float)avoidance.desiredDirection.y, 0) * velocityScale;
// //                         Gizmos.DrawLine(position, desiredEnd);
// //                     }
// //
// //                     // Draw avoidance radius
// //                     if (showAvoidanceForces)
// //                     {
// //                         Gizmos.color = new Color(avoidanceColor.r, avoidanceColor.g, avoidanceColor.b, 0.1f);
// //                         Gizmos.DrawWireSphere(position, (float)follower.avoidanceRadius);
// //                     }
// //                 }
// //                 else
// //                 {
// //                     // For non-avoidance units, show simplified info
// //                     Gizmos.color = Color.white;
// //                     Gizmos.DrawWireSphere(position, unitSize * 0.8f);
// //                 }
// //             }
// //
// //             Gizmos.color = originalColor;
// //         }
// //
// //         void DrawTarget()
// //         {
// //             if (!entityManager.Exists(targetEntity)) return;
// //
// //             var transform = entityManager.GetComponentData<LocalTransform>(targetEntity);
// //             var originalColor = Gizmos.color;
// //
// //             Gizmos.color = targetColor;
// //             Gizmos.DrawWireSphere(transform.Position, 0.8f);
// //             Gizmos.DrawSphere(transform.Position, 0.5f);
// //
// //             Gizmos.color = originalColor;
// //         }
// //
// //         void Update()
// //         {
// //             // Ensure the flow field buffer is always up to date for visualization
// //             if (entityManager.Exists(flowFieldEntity) && entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity))
// //             {
// //                 flowFieldBuffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //             }
// //
// //             // Allow runtime target movement with mouse
// //             if (Input.GetMouseButton(0) && Camera.main != null)
// //             {
// //                 var mousePos = Input.mousePosition;
// //                 var worldPos = Camera.main.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, 10));
// //
// //                 if (entityManager.Exists(targetEntity))
// //                 {
// //                     MoveTarget(new float3(worldPos.x, worldPos.y, 0));
// //                 }
// //             }
// //         }
// //
// //         void OnDestroy()
// //         {
// //             // Clean up entities safely
// //             if (entityManager != null)
// //             {
// //                 if (entityManager.Exists(flowFieldEntity))
// //                     entityManager.DestroyEntity(flowFieldEntity);
// //
// //                 if (entityManager.Exists(targetEntity))
// //                     entityManager.DestroyEntity(targetEntity);
// //
// //                 if (testUnits != null)
// //                 {
// //                     foreach (var unit in testUnits)
// //                     {
// //                         if (entityManager.Exists(unit))
// //                             entityManager.DestroyEntity(unit);
// //                     }
// //                 }
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Validate that the visualizer is properly set up
// //         /// </summary>
// //         public bool IsValid()
// //         {
// //             return world != null &&
// //                    entityManager != null &&
// //                    entityManager.Exists(flowFieldEntity) &&
// //                    entityManager.Exists(targetEntity) &&
// //                    entityManager.HasBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //         }
// //
// //         /// <summary>
// //         /// Get debug information about the current flow field state
// //         /// </summary>
// //         public string GetDebugInfo()
// //         {
// //             if (!IsValid()) return "FlowFieldVisualizer is not valid";
// //
// //             var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntity);
// //             var buffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //
// //             // Check target cell
// //             var targetTransform = entityManager.GetComponentData<LocalTransform>(targetEntity);
// //             var targetGridPos = FlowFieldGridUtils.WorldToGrid(
// //                 targetTransform.Position.xy, flowFieldGrid.worldOrigin, flowFieldGrid.cellSize);
// //             var targetIndex = FlowFieldGridUtils.GridToIndex(targetGridPos, flowFieldGrid.gridSize);
// //             var targetCell = buffer.Length > targetIndex ? buffer[targetIndex].cell : new FlowFieldCell();
// //
// //             // Check a few sample cells
// //             var sampleInfo = "";
// //             for (int i = 0; i < math.min(5, buffer.Length); i++)
// //             {
// //                 var cell = buffer[i].cell;
// //                 sampleInfo +=
// //                     $"Cell[{i}]: dir=({cell.direction.x:F2},{cell.direction.y:F2}), cost={cell.cost:F2}, dist={cell.distance:F2}, target={cell.isTarget}\n";
// //             }
// //
// //             return $"Flow Field Debug Info:\n" +
// //                    $"Grid Size: {flowFieldGrid.gridSize}\n" +
// //                    $"Cell Size: {flowFieldGrid.cellSize}\n" +
// //                    $"World Origin: {flowFieldGrid.worldOrigin}\n" +
// //                    $"Movement Type: {flowFieldGrid.movementType}\n" +
// //                    $"Needs Update: {flowFieldGrid.needsUpdate}\n" +
// //                    $"Last Update Time: {flowFieldGrid.lastUpdateTime}\n" +
// //                    $"Update Interval: {flowFieldGrid.updateInterval}\n" +
// //                    $"Buffer Length: {buffer.Length}\n" +
// //                    $"Target Position: {targetTransform.Position}\n" +
// //                    $"Target Grid Pos: {targetGridPos}\n" +
// //                    $"Target Index: {targetIndex}\n" +
// //                    $"Target Cell: dir=({targetCell.direction.x:F2},{targetCell.direction.y:F2}), isTarget={targetCell.isTarget}\n" +
// //                    $"Test Units: {testUnits?.Count ?? 0}\n" +
// //                    $"Sample Cells:\n{sampleInfo}";
// //         }
// //
// //         /// <summary>
// //         /// Test flow field sampling at a specific world position
// //         /// </summary>
// //         public string TestFlowFieldSampling(Vector3 worldPos)
// //         {
// //             if (!IsValid()) return "FlowFieldVisualizer is not valid";
// //
// //             var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntity);
// //             var buffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
// //
// //             var worldPos2D = new dfloat2((dfloat)worldPos.x, (dfloat)worldPos.y);
// //             var direction = FlowFieldUtils.SampleFlowField(buffer, worldPos2D, flowFieldGrid.worldOrigin,
// //                 flowFieldGrid.cellSize, flowFieldGrid.gridSize, movementType);
// //
// //             var gridPos = FlowFieldGridUtils.WorldToGrid(new float2(worldPos.x, worldPos.y),
// //                 flowFieldGrid.worldOrigin, flowFieldGrid.cellSize);
// //             var isValid = FlowFieldGridUtils.IsValidGridPosition(gridPos, flowFieldGrid.gridSize);
// //
// //             return $"Flow Field Sampling Test:\n" +
// //                    $"World Position: {worldPos}\n" +
// //                    $"Grid Position: {gridPos}\n" +
// //                    $"Valid Grid Position: {isValid}\n" +
// //                    $"Sampled Direction: ({direction.x:F3}, {direction.y:F3})\n" +
// //                    $"Direction Magnitude: {math.sqrt((float)(direction.x * direction.x + direction.y * direction.y)):F3}";
// //         }
// //
// //         /// <summary>
// //         /// Apply a slow effect to all test units
// //         /// </summary>
// //         public void ApplySlowToAllUnits(float slowAmount = 0.5f, float duration = 5.0f)
// //         {
// //             if (testUnits == null) return;
// //
// //             var currentTime = new dfloat(Time.time);
// //             foreach (var unit in testUnits)
// //             {
// //                 if (!entityManager.Exists(unit)) continue;
// //                 if (!entityManager.HasBuffer<GameEffect>(unit)) continue;
// //
// //                 var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(unit);
// //                 GameEffectUtils.ApplySlowEffect(gameEffectBuffer, new dfloat(slowAmount),
// //                     new dfloat(duration), currentTime, 999);
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Apply a haste effect to all test units
// //         /// </summary>
// //         public void ApplyHasteToAllUnits(float hasteAmount = 2.0f, float duration = 3.0f)
// //         {
// //             if (testUnits == null) return;
// //
// //             var currentTime = new dfloat(Time.time);
// //             foreach (var unit in testUnits)
// //             {
// //                 if (!entityManager.Exists(unit)) continue;
// //                 if (!entityManager.HasBuffer<GameEffect>(unit)) continue;
// //
// //                 var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(unit);
// //                 GameEffectUtils.ApplyHasteEffect(gameEffectBuffer, new dfloat(hasteAmount),
// //                     new dfloat(duration), currentTime, 998);
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Clear all speed effects from test units
// //         /// </summary>
// //         public void ClearAllSpeedEffects()
// //         {
// //             if (testUnits == null) return;
// //
// //             foreach (var unit in testUnits)
// //             {
// //                 if (!entityManager.Exists(unit)) continue;
// //                 if (!entityManager.HasBuffer<GameEffect>(unit)) continue;
// //
// //                 var gameEffectBuffer = entityManager.GetBuffer<GameEffect>(unit);
// //                 gameEffectBuffer.Clear();
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Get movement effects information for debugging
// //         /// </summary>
// //         public string GetSpeedModifierInfo()
// //         {
// //             if (testUnits == null || testUnits.Count == 0) return "No test units available";
// //
// //             var info = "Movement Effects Info:\n";
// //             int unitCount = 0;
// //             var currentTime = new dfloat(Time.time);
// //
// //             foreach (var unit in testUnits)
// //             {
// //                 if (!entityManager.Exists(unit)) continue;
// //                 if (!entityManager.HasComponent<FlowField.Effects.MovementEffects>(unit)) continue;
// //
// //                 var movementEffects = entityManager.GetComponentData<FlowField.Effects.MovementEffects>(unit);
// //                 var follower = entityManager.GetComponentData<FlowFieldFollower>(unit);
// //                 var gameEffects = entityManager.GetBuffer<FlowField.Effects.GameEffect>(unit);
// //
// //                 var activeEffects = 0;
// //                 for (int i = 0; i < gameEffects.Length; i++)
// //                 {
// //                     if (!gameEffects[i].IsExpired(currentTime))
// //                         activeEffects++;
// //                 }
// //
// //                 info += $"Unit {unitCount}: " +
// //                         $"Speed={follower.maxSpeed:F2} (base={movementEffects.baseMaxSpeed:F2}, mult={movementEffects.speedMultiplier:F2}), " +
// //                         $"Effects={activeEffects}, " +
// //                         $"Slowed={movementEffects.isSlowed}, Hasted={movementEffects.isHasted}, Stunned={movementEffects.isStunned}\n";
// //
// //                 unitCount++;
// //                 if (unitCount >= 5) break; // Limit to first 5 units for readability
// //             }
// //
// //             return info;
// //         }
// //
// //         /// <summary>
// //         /// Get fixed timestep information for debugging
// //         /// </summary>
// //         public string GetFixedTimestepInfo()
// //         {
// //             // Find the fixed timestep entity
// //             var query = entityManager.CreateEntityQuery(typeof(FixedTimestep));
// //             if (query.IsEmpty) return "No fixed timestep found";
// //
// //             var fixedTimestep = query.GetSingleton<FixedTimestep>();
// //
// //             return $"Fixed Timestep Info:\n" +
// //                    $"Tick Rate: {fixedTimestep.tickRate:F1} Hz\n" +
// //                    $"Tick Duration: {fixedTimestep.tickDuration:F3} seconds\n" +
// //                    $"Current Time: {fixedTimestep.currentTime:F3} seconds\n" +
// //                    $"Tick Count: {fixedTimestep.tickCount}\n" +
// //                    $"Accumulator: {fixedTimestep.accumulator:F3} seconds\n" +
// //                    $"Should Process Tick: {fixedTimestep.shouldProcessTick}\n" +
// //                    $"Interpolation Alpha: {fixedTimestep.interpolationAlpha:F3}\n" +
// //                    $"Interpolation Quality: {(fixedTimestep.interpolationAlpha >= 0 && fixedTimestep.interpolationAlpha <= 1 ? "Good" : "ERROR - Out of Range!")}";
// //         }
// //
// //         /// <summary>
// //         /// Get interpolation system information for debugging
// //         /// </summary>
// //         public string GetInterpolationInfo()
// //         {
// //             if (!Application.isPlaying) return "Not playing";
// //
// //             // Count entities with interpolation components
// //             var simulationQuery = entityManager.CreateEntityQuery(typeof(SimulationTransform));
// //             var localTransformQuery = entityManager.CreateEntityQuery(typeof(LocalTransform));
// //             var previousQuery = entityManager.CreateEntityQuery(typeof(PreviousTransform));
// //
// //             var simulationCount = simulationQuery.CalculateEntityCount();
// //             var localTransformCount = localTransformQuery.CalculateEntityCount();
// //             var previousCount = previousQuery.CalculateEntityCount();
// //
// //             return $"Interpolation System Status:\n" +
// //                    $"Entities with SimulationTransform: {simulationCount}\n" +
// //                    $"Entities with LocalTransform: {localTransformCount}\n" +
// //                    $"Entities with PreviousTransform: {previousCount}\n" +
// //                    $"Interpolation System Active: {simulationCount > 0 && localTransformCount > 0 && previousCount > 0}";
// //         }
// //
// //         /// <summary>
// //         /// Update target proximity threshold for all units at runtime
// //         /// </summary>
// //         public void UpdateTargetProximityThreshold(float newThreshold)
// //         {
// //             if (!Application.isPlaying) return;
// //
// //             var query = entityManager.CreateEntityQuery(typeof(FlowFieldFollower));
// //             var entities = query.ToEntityArray(Allocator.Temp);
// //
// //             for (int i = 0; i < entities.Length; i++)
// //             {
// //                 var entity = entities[i];
// //                 var follower = entityManager.GetComponentData<FlowFieldFollower>(entity);
// //
// //                 if (follower.useAvoidance)
// //                 {
// //                     follower.targetProximityThreshold = new dfloat(newThreshold);
// //                 }
// //                 else
// //                 {
// //                     follower.targetProximityThreshold = new dfloat(newThreshold * 0.5f);
// //                 }
// //
// //                 entityManager.SetComponentData(entity, follower);
// //             }
// //
// //             entities.Dispose();
// //             targetProximityThreshold = newThreshold;
// //         }
// //
// //         /// <summary>
// //         /// Initialize all visualization components
// //         /// </summary>
// //         private void InitializeVisualizationComponents()
// //         {
// //             visualizationComponents.Clear();
// //
// //             // Create or find visualization components
// //             if (autoCreateComponents)
// //             {
// //                 CreateVisualizationComponents();
// //             }
// //             else
// //             {
// //                 FindExistingVisualizationComponents();
// //             }
// //
// //             // Initialize all components
// //             foreach (var component in visualizationComponents)
// //             {
// //                 if (component != null)
// //                 {
// //                     component.Initialize(entityManager, world);
// //                 }
// //             }
// //
// //             // Configure components with current data
// //             ConfigureVisualizationComponents();
// //         }
// //
// //         /// <summary>
// //         /// Create visualization components if they don't exist
// //         /// </summary>
// //         private void CreateVisualizationComponents()
// //         {
// //             // Grid Visualization
// //             if (gridVisualization == null && enableGridVisualization)
// //             {
// //                 gridVisualization = gameObject.AddComponent<GridVisualization>();
// //             }
// //             if (gridVisualization != null)
// //             {
// //                 gridVisualization.IsEnabled = enableGridVisualization;
// //                 visualizationComponents.Add(gridVisualization);
// //             }
// //
// //             // Unit Visualization
// //             if (unitVisualization == null && enableUnitVisualization)
// //             {
// //                 unitVisualization = gameObject.AddComponent<UnitVisualization>();
// //             }
// //             if (unitVisualization != null)
// //             {
// //                 unitVisualization.IsEnabled = enableUnitVisualization;
// //                 visualizationComponents.Add(unitVisualization);
// //             }
// //
// //             // Target Visualization
// //             if (targetVisualization == null && enableTargetVisualization)
// //             {
// //                 targetVisualization = gameObject.AddComponent<TargetVisualization>();
// //             }
// //             if (targetVisualization != null)
// //             {
// //                 targetVisualization.IsEnabled = enableTargetVisualization;
// //                 visualizationComponents.Add(targetVisualization);
// //             }
// //
// //             // Unit Path Visualization
// //             if (unitPathVisualization == null && enableUnitPathVisualization)
// //             {
// //                 unitPathVisualization = gameObject.AddComponent<UnitPathVisualization>();
// //             }
// //             if (unitPathVisualization != null)
// //             {
// //                 unitPathVisualization.IsEnabled = enableUnitPathVisualization;
// //                 visualizationComponents.Add(unitPathVisualization);
// //             }
// //
// //             // Projectile Visualization
// //             if (projectileVisualization == null && enableProjectileVisualization)
// //             {
// //                 projectileVisualization = gameObject.AddComponent<ProjectileVisualization>();
// //             }
// //             if (projectileVisualization != null)
// //             {
// //                 projectileVisualization.IsEnabled = enableProjectileVisualization;
// //                 visualizationComponents.Add(projectileVisualization);
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Find existing visualization components on this GameObject
// //         /// </summary>
// //         private void FindExistingVisualizationComponents()
// //         {
// //             if (gridVisualization == null)
// //                 gridVisualization = GetComponent<GridVisualization>();
// //             if (gridVisualization != null)
// //             {
// //                 gridVisualization.IsEnabled = enableGridVisualization;
// //                 visualizationComponents.Add(gridVisualization);
// //             }
// //
// //             if (unitVisualization == null)
// //                 unitVisualization = GetComponent<UnitVisualization>();
// //             if (unitVisualization != null)
// //             {
// //                 unitVisualization.IsEnabled = enableUnitVisualization;
// //                 visualizationComponents.Add(unitVisualization);
// //             }
// //
// //             if (targetVisualization == null)
// //                 targetVisualization = GetComponent<TargetVisualization>();
// //             if (targetVisualization != null)
// //             {
// //                 targetVisualization.IsEnabled = enableTargetVisualization;
// //                 visualizationComponents.Add(targetVisualization);
// //             }
// //
// //             if (unitPathVisualization == null)
// //                 unitPathVisualization = GetComponent<UnitPathVisualization>();
// //             if (unitPathVisualization != null)
// //             {
// //                 unitPathVisualization.IsEnabled = enableUnitPathVisualization;
// //                 visualizationComponents.Add(unitPathVisualization);
// //             }
// //
// //             if (projectileVisualization == null)
// //                 projectileVisualization = GetComponent<ProjectileVisualization>();
// //             if (projectileVisualization != null)
// //             {
// //                 projectileVisualization.IsEnabled = enableProjectileVisualization;
// //                 visualizationComponents.Add(projectileVisualization);
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Configure visualization components with current FlowFieldVisualizer data
// //         /// </summary>
// //         private void ConfigureVisualizationComponents()
// //         {
// //             // Configure Grid Visualization
// //             if (gridVisualization != null)
// //             {
// //                 gridVisualization.FlowFieldEntity = flowFieldEntity;
// //                 gridVisualization.GridSize = gridSize;
// //                 gridVisualization.CellSize = cellSize;
// //                 gridVisualization.WorldOrigin = worldOrigin;
// //                 gridVisualization.MovementType = movementType;
// //             }
// //
// //             // Configure Unit Visualization
// //             if (unitVisualization != null)
// //             {
// //                 unitVisualization.UnitsToVisualize = testUnits;
// //                 unitVisualization.TargetEntity = targetEntity;
// //                 unitVisualization.AutoFindUnits = true;
// //             }
// //
// //             // Configure Target Visualization
// //             if (targetVisualization != null)
// //             {
// //                 if (targetEntity != Entity.Null)
// //                 {
// //                     targetVisualization.TargetsToVisualize = new List<Entity> { targetEntity };
// //                 }
// //                 targetVisualization.AutoFindTargets = true;
// //             }
// //
// //             // Configure Unit Path Visualization
// //             if (unitPathVisualization != null)
// //             {
// //                 unitPathVisualization.UnitsToTrack = testUnits;
// //                 unitPathVisualization.AutoFindUnits = true;
// //             }
// //
// //             // Configure Projectile Visualization
// //             if (projectileVisualization != null)
// //             {
// //                 projectileVisualization.AutoFindProjectiles = true;
// //             }
// //         }
// //
// //         /// <summary>
// //         /// Update visualization component states based on current settings
// //         /// </summary>
// //         public void UpdateVisualizationStates()
// //         {
// //             if (gridVisualization != null)
// //                 gridVisualization.IsEnabled = enableGridVisualization;
// //             if (unitVisualization != null)
// //                 unitVisualization.IsEnabled = enableUnitVisualization;
// //             if (targetVisualization != null)
// //                 targetVisualization.IsEnabled = enableTargetVisualization;
// //             if (unitPathVisualization != null)
// //                 unitPathVisualization.IsEnabled = enableUnitPathVisualization;
// //             if (projectileVisualization != null)
// //                 projectileVisualization.IsEnabled = enableProjectileVisualization;
// //         }
// //
// //         /// <summary>
// //         /// Get detailed information from all visualization components
// //         /// </summary>
// //         public string GetVisualizationInfo()
// //         {
// //             var info = "FlowField Visualization Components:\n";
// //
// //             foreach (var component in visualizationComponents)
// //             {
// //                 if (component != null)
// //                 {
// //                     info += $"- {component.VisualizationName}: {(component.IsEnabled ? "Enabled" : "Disabled")}\n";
// //                     if (component.ShowDetailedInfo)
// //                     {
// //                         info += $"  {component.GetDetailedInfo()}\n";
// //                     }
// //                 }
// //             }
// //
// //             return info;
// //         }
// //     }
// // }